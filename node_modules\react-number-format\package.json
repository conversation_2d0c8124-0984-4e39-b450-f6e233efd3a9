{"name": "react-number-format", "description": "React component to format number in an input or as a text.", "version": "5.4.4", "main": "dist/react-number-format.cjs.js", "module": "dist/react-number-format.es.js", "types": "types/index.d.ts", "files": ["dist", "types"], "sideEffects": false, "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/s-yadav/react-number-format"}, "homepage": "https://s-yadav.github.io/react-number-format/docs/intro", "bugs": {"mail": "<EMAIL>", "url": "https://github.com/s-yadav/react-number-format/issues"}, "keywords": ["react-component", "react", "currency", "input", "number", "number-format", "mask"], "scripts": {"start": "webpack-dev-server --hot", "start-doc": "docusaurus start documentation/v5 --port 9000", "build-doc": "docusaurus build documentation/v5", "publish-doc": "node publish_doc.js", "format": "prettier --write ./src ./test ./example", "build": "cross-env yarn build-dist && yarn build-types && yarn test-build && yarn test-ts && yarn format && yarn build-doc && yarn publish-doc", "build-dist": "cross-env NODE_ENV=production rollup -c rollup.config.js", "build-types": "tsc -p tsconfig.build.json", "test": "vitest && yarn test-ts", "coverage": "vitest run --coverage", "test-build": "vitest run", "test-ts": "yarn tsc --noEmit", "lint": "cross-env eslint src/**"}, "devDependencies": {"@babel/cli": "^7.6.2", "@babel/core": "^7.6.2", "@babel/plugin-proposal-class-properties": "^7.5.5", "@babel/plugin-proposal-object-rest-spread": "^7.6.2", "@babel/plugin-transform-flow-strip-types": "^7.4.4", "@babel/preset-env": "^7.6.2", "@babel/preset-react": "^7.0.0", "@babel/register": "^7.6.2", "@cmfcmf/docusaurus-search-local": "^0.11.0", "@docusaurus/core": "2.0.0-beta.18", "@docusaurus/preset-classic": "2.0.0-beta.18", "@mdx-js/react": "^1.6.22", "@rollup/plugin-buble": "^0.21.3", "@rollup/plugin-typescript": "^8.3.0", "@testing-library/jest-dom": "^6.4.5", "@testing-library/react": "^15.0.7", "@testing-library/user-event": "^14.5.2", "@types/react": "^18.0.6", "@typescript-eslint/parser": "^5.15.0", "babel-eslint": "^10.0.3", "babel-loader": "^8.0.6", "babel-plugin-add-module-exports": "^1.0.2", "babel-plugin-transform-object-assign": "^6.22.0", "classnames": "^2.2.6", "clsx": "^1.1.1", "cross-env": "^6.0.3", "eslint": "^6.5.1", "eslint-config-prettier": "^8.3.0", "eslint-loader": "^3.0.2", "eslint-plugin-import": "^2.18.2", "eslint-plugin-react": "^7.15.1", "gh-pages": "^4.0.0", "jsdom": "^24.0.0", "material-ui": "^0.20.2", "pascal-case": "3.1.2", "prettier": "^2.2.1", "prism-react-renderer": "^1.3.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-transform-hmr": "^1.0.4", "rollup": "^1.22.0", "rollup-plugin-babel": "^4.3.3", "rollup-plugin-commonjs": "^10.1.0", "rollup-plugin-filesize": "^6.2.0", "rollup-plugin-license": "^0.12.1", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-uglify": "^6.0.3", "ts-loader": "^9.2.6", "typescript": "^4.6.3", "vitest": "^1.6.0", "webpack": "^5.69.1", "webpack-cli": "^4.9.2", "webpack-dev-server": "^4.7.4"}, "peerDependencies": {"react": "^0.14 || ^15.0.0 || ^16.0.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^0.14 || ^15.0.0 || ^16.0.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"}, "packageManager": "yarn@1.22.19"}