!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("react")):"function"==typeof define&&define.amd?define(["exports","react"],t):t((e=e||self).NumberFormat={},e.React)}(this,function(e,K){"use strict";var W,t,L="default"in K?K.default:K;function $(e,t){var r={};for(a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(r[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,a=Object.getOwnPropertySymbols(e);n<a.length;n++)t.indexOf(a[n])<0&&Object.prototype.propertyIsEnumerable.call(e,a[n])&&(r[a[n]]=e[a[n]]);return r}function G(){}function U(e){return!!(e||"").match(/\d/)}function N(e){return null==e}function D(e){return N(e)||"number"==typeof(t=e)&&isNaN(t)||"number"==typeof e&&!isFinite(e);var t}function h(e){return e.replace(/[-[\]/{}()*+?.\\^$|]/g,"\\$&")}function b(e,t,r){var r=function(e){switch(e){case"lakh":return/(\d+?)(?=(\d\d)+(\d)(?!\d))(\.\d+)?/g;case"wan":return/(\d)(?=(\d{4})+(?!\d))/g;default:return/(\d)(?=(\d{3})+(?!\d))/g}}(r),n=-1===(n=e.search(/[1-9]/))?e.length:n;return e.substring(0,n)+e.substring(n,e.length).replace(r,"$1"+t)}function S(e,t){void 0===t&&(t=!0);var r="-"===e[0],t=r&&t,e=(e=e.replace("-","")).split(".");return{beforeDecimal:e[0],afterDecimal:e[1]||"",hasNegation:r,addNegation:t}}function d(e,t,r){for(var n="",a=r?"0":"",o=0;o<=t-1;o++)n+=e[o]||a;return n}function a(e,t){return Array(t+1).join(e)}function O(e){var t,e=e+"",r="-"===e[0]?"-":"",e=(e=r?e.substring(1):e).split(/[eE]/g),n=e[0],e=e[1];return(e=Number(e))&&(e=1+e,t=(n=n.replace(".","")).length,e<0?n="0."+a("0",Math.abs(e))+n:t<=e?n+=a("0",e-t):n=(n.substring(0,e)||"0")+"."+n.substring(e)),r+n}function E(e,t,r){var n,a,o,u;return-1!==["","-"].indexOf(e)?e:(n=(-1!==e.indexOf(".")||r)&&t,a=(e=S(e)).beforeDecimal,o=e.afterDecimal,e=e.hasNegation,u=parseFloat("0."+(o||"0")),o=(o.length<=t?"0."+o:u.toFixed(t)).split("."),(e?"-":"")+(u=(u=a)&&Number(o[0])?a.split("").reverse().reduce(function(e,t,r){return e.length>r?(Number(e[0])+Number(t)).toString()+e.substring(1,e.length):t+e},o[0]):u)+(n?".":"")+d(o[1]||"",t,r))}function q(e,t){var r;e.value=e.value,null!==e&&(e.createTextRange?((r=e.createTextRange()).move("character",t),r.select()):e.selectionStart||0===e.selectionStart?(e.focus(),e.setSelectionRange(t,t)):e.focus())}(t=W=W||{}).event="event",t.props="prop";r=function(e,t){for(var r=0,n=0,a=e.length,o=t.length;e[r]===t[r]&&r<a;)r++;for(;e[a-1-n]===t[o-1-n]&&r<o-n&&r<a-n;)n++;return{from:{start:r,end:a-n},to:{start:r,end:o-n}}},o=void 0;var r,n,o,Z=function(){for(var e=[],t=arguments.length;t--;)e[t]=arguments[t];return n&&e.length===n.length&&e.every(function(e,t){return e===n[t]})||(n=e,o=r.apply(void 0,e)),o};function _(e){return Math.max(e.selectionStart,e.selectionEnd)}function y(e){return{from:{start:0,end:0},to:{start:0,end:e.length},lastValue:""}}function m(e,t){return"string"==typeof(e=void 0===e?" ":e)?e:e[t]||" "}function J(e){var t=e.currentValue,r=e.formattedValue,n=e.currentValueIndex,e=e.formattedValueIndex;return t[n]===r[e]}function z(e,t,r,n){var a,o,u=e.length;if(e=t,a=0,o=u,t=Math.min(Math.max(e,a),o),"left"===n){for(;0<=t&&!r[t];)t--;-1===t&&(t=r.indexOf(!0))}else{for(;t<=u&&!r[t];)t++;u<t&&(t=r.lastIndexOf(!0))}return t=-1===t?u:t}function Q(e){for(var t=Array.from({length:e.length+1}).map(function(){return!0}),r=0,n=t.length;r<n;r++)t[r]=Boolean(U(e[r])||U(e[r-1]));return t}function H(e,t,r,n,a,o){void 0===o&&(o=G);l=function(e,t){var r,t=D(e)?r="":(r="number"==typeof e||t?"number"==typeof e?O(e):e:a(e,void 0),n(r));return{formattedValue:t,numAsString:r}},(u=K.useRef(l)).current=l;var u,i=K.useRef(function(){for(var e=[],t=arguments.length;t--;)e[t]=arguments[t];return u.current.apply(u,e)}).current,l=K.useState(function(){return i(N(e)?t:e,r)}),c=l[0],s=l[1],l=e,f=r,d=(N(e)&&(l=c.numAsString,f=!0),i(l,f));return K.useMemo(function(){s(d)},[d.formattedValue]),[c,function(e,t){e.formattedValue!==c.formattedValue&&s({formattedValue:e.formattedValue,numAsString:e.value}),o(e,t)}]}function X(e){return e.replace(/[^0-9]/g,"")}function Y(e){return e}function u(e){function i(e,t){F.current={formattedValue:e.formattedValue,numAsString:e.value},E(e,t)}function c(e,t,r){return z(e,t,w(e),r)}function r(e,t,r){var n,a,o=t.target,u=C.current?(u=C.current,o=o.selectionEnd,{from:{start:i=Math.min(u.selectionStart,o),end:u.selectionEnd},to:{start:i,end:o}}):Z(D,e),i=Object.assign(Object.assign({},u),{lastValue:D}),o=f(e,i),l=R(o),o=f(l,void 0);return v&&!v(I(l,o))?(a=_(n=t.target),a=k(e,D,a),n.value=D,T(n,a,D),!1):(M({formattedValue:l,numAsString:o,inputValue:e,event:t,source:r,input:t.target}),!0)}function s(e,t){var r=e.selectionStart,e=e.selectionEnd;C.current={selectionStart:r,selectionEnd:e+(t=void 0===t?0:t)}}var t=e.type,n=(void 0===t&&(t="text"),e.displayType),a=(void 0===n&&(n="input"),e.customInput),o=e.renderText,u=e.getInputRef,l=e.format,f=(void 0===l&&(l=Y),e.removeFormatting),d=(void 0===f&&(f=X),e.defaultValue),m=e.valueIsNumericString,g=e.onValueChange,v=e.isAllowed,p=e.onChange,h=(void 0===p&&(p=G),e.onKeyDown),S=(void 0===h&&(h=G),e.onMouseUp),y=(void 0===S&&(S=G),e.onFocus),b=(void 0===y&&(y=G),e.onBlur),V=(void 0===b&&(b=G),e.value),w=e.getCaretBoundary,x=(void 0===w&&(w=Q),e.isValidInputCharacter),N=(void 0===x&&(x=U),e.isCharacterSame),e=$(e,["type","displayType","customInput","renderText","getInputRef","format","removeFormatting","defaultValue","valueIsNumericString","onValueChange","isAllowed","onChange","onKeyDown","onMouseUp","onFocus","onBlur","value","getCaretBoundary","isValidInputCharacter","isCharacterSame"]),V=H(V,d,Boolean(m),l,f,g),d=V[0],D=d.formattedValue,O=d.numAsString,E=V[1],C=K.useRef(),F=K.useRef({formattedValue:D,numAsString:O}),m=K.useState(!1),g=m[0],j=m[1],A=K.useRef(null),B=K.useRef({setCaretTimeout:null,focusTimeout:null}),R=(K.useEffect(function(){return j(!0),function(){clearTimeout(B.current.setCaretTimeout),clearTimeout(B.current.focusTimeout)}},[]),l),I=function(e,t){var r=parseFloat(t);return{formattedValue:e,value:t,floatValue:isNaN(r)?void 0:r}},T=function(e,t,r){0===e.selectionStart&&e.selectionEnd===e.value.length||(q(e,t),B.current.setCaretTimeout=setTimeout(function(){e.value===r&&e.selectionStart!==t&&q(e,t)},0))},k=function(e,t,r){var n=w(t);return z(t,function(e,t,r,n,a,o,u){void 0===u&&(u=J);for(var a=a.findIndex(function(e){return e}),a=e.slice(0,a),i=(t||r.startsWith(a)||(r=(t=a)+r,n+=a.length),r.length),l=e.length,c={},s=new Array(i),f=0;f<i;f++){s[f]=-1;for(var d=0,m=l;d<m;d++)if(u({currentValue:r,lastValue:t,formattedValue:e,currentValueIndex:f,formattedValueIndex:d})&&!0!==c[d]){c[s[f]=d]=!0;break}}for(var g=n;g<i&&(-1===s[g]||!o(r[g]));)g++;for(a=g===i||-1===s[g]?l:s[g],g=n-1;0<g&&-1===s[g];)g--;var v=-1===g||-1===s[g]?0:s[g]+1;return!(a<v)&&n-v<a-n?v:a}(t,D,e,r,n,x,N),n)},M=function(e){var t,r=e.formattedValue,n=(void 0===r&&(r=""),e.input),a=e.source,o=e.event,u=e.numAsString;n&&(e=e.inputValue||n.value,t=_(n),n.value=r,void 0!==(e=k(e,r,t))&&T(n,e,r)),r!==D&&i(I(r,u),{event:o,source:a})},P=(K.useEffect(function(){var e=F.current,t=e.formattedValue,e=e.numAsString;D===t&&O===e||i(I(D,O),{event:void 0,source:W.props})},[D,O]),A.current?_(A.current):void 0),d=(("undefined"!=typeof window?K.useLayoutEffect:K.useEffect)(function(){var e,t=A.current;D!==F.current.formattedValue&&t&&(e=k(F.current.formattedValue,D,P),t.value=D,T(t,e,D))},[D]),g&&!("undefined"==typeof navigator||navigator.platform&&/iPhone|iPod/.test(navigator.platform))?"numeric":void 0),V=Object.assign({inputMode:d},e,{type:t,value:D,onChange:function(e){var t=e.target.value;r(t,e,W.event)&&p(e),C.current=void 0},onKeyDown:function(e){var t,r=e.target,n=e.key,a=r.selectionStart,o=r.selectionEnd,u=r.value,i=(void 0===u&&(u=""),"ArrowLeft"===n||"Backspace"===n?t=Math.max(a-1,0):"ArrowRight"===n?t=Math.min(a+1,u.length):"Delete"===n&&(t=a),0),l=("Delete"===n&&a===o&&(i=1),"ArrowLeft"===n||"ArrowRight"===n);void 0===t||a!==o&&!l||(a=t,l?(a=c(u,t,"ArrowLeft"===n?"left":"right"))!==t&&e.preventDefault():"Delete"!==n||x(u[t])?"Backspace"!==n||x(u[t])||(a=c(u,t,"left")):a=c(u,t,"right"),a!==t&&T(r,a,u)),h(e),s(r,i)},onMouseUp:function(e){function t(){var e=n.selectionStart,t=n.selectionEnd,r=n.value;void 0===r&&(r=""),e===t&&(t=c(r,e))!==e&&T(n,t,r)}var n=e.target;t(),requestAnimationFrame(function(){t()}),S(e),s(n)},onFocus:function(a){a.persist&&a.persist();var o=a.target,u=a.currentTarget;A.current=o,B.current.focusTimeout=setTimeout(function(){var e=o.selectionStart,t=o.selectionEnd,r=o.value,n=c(r=void 0===r?"":r,e);n===e||0===e&&t===r.length||T(o,n,r),y(Object.assign(Object.assign({},a),{currentTarget:u}))},0)},onBlur:function(e){A.current=null,clearTimeout(B.current.focusTimeout),clearTimeout(B.current.setCaretTimeout),b(e)}});return"text"===n?o?L.createElement(L.Fragment,null,o(D,e)||null):L.createElement("span",Object.assign({},e,{ref:u}),D):a?L.createElement(a,Object.assign({},V,{ref:u})):L.createElement("input",Object.assign({},V,{ref:u}))}function C(e,t){var r,n,a,o,u=t.decimalScale,i=t.fixedDecimalScale,l=t.prefix,c=(void 0===l&&(l=""),t.suffix),s=(void 0===c&&(c=""),t.allowNegative),f=t.thousandsGroupStyle;return void 0===f&&(f="thousand"),""!==e&&"-"!==e&&(r=(t=F(t)).thousandSeparator,t=t.decimalSeparator,n=0!==u&&-1!==e.indexOf(".")||u&&i,a=(s=S(e,s)).beforeDecimal,o=s.afterDecimal,s=s.addNegation,void 0!==u&&(o=d(o,u,!!i)),r&&(a=b(a,r,f)),l&&(a=l+a),c&&(o+=c),e=(a=s?"-"+a:a)+(n&&t||"")+o),e}function F(e){var t=e.decimalSeparator,r=e.thousandSeparator,e=e.allowedDecimalSeparators;return{decimalSeparator:t=void 0===t?".":t,thousandSeparator:r=!0===r?",":r,allowedDecimalSeparators:e=e||[t,"."]}}function j(e,t,r){void 0===t&&(t=y(e));var n,a,o,u,i=r.allowNegative,l=r.prefix,c=(void 0===l&&(l=""),r.suffix),s=(void 0===c&&(c=""),r.decimalScale),f=t.from,d=t.to,m=d.start,g=d.end,r=F(r),v=r.allowedDecimalSeparators,r=r.decimalSeparator,p=e[g]===r;return U(e)&&(e===l||e===c)&&""===t.lastValue||(g-m==1&&-1!==v.indexOf(e[m])&&(v=0===s?"":r,e=e.substring(0,m)+v+e.substring(m+1,e.length)),v=(s=function(e,t,r){var n=!1,a=!1,o=(l.startsWith("-")?n=!1:e.startsWith("--")?a=!(n=!1):c.startsWith("-")&&e.length===c.length?n=!1:"-"===e[0]&&(n=!0),n?1:0);return(o=a?2:o)&&(e=e.substring(o),t-=o,r-=o),{value:e,start:t,end:r,hasNegation:n}})(e,m,g),u=v.hasNegation,e=v.value,m=v.start,g=v.end,s=(v=s(t.lastValue,f.start,f.end)).start,t=v.end,v=v.value,n=e.substring(m,g),a=0,(e=!(e.length&&v.length&&(s>v.length-c.length||t<l.length))||n&&c.startsWith(n)?e:v).startsWith(l)?a+=l.length:m<l.length&&(a=m),g-=a,s=(e=e.substring(a)).length,t=e.length-c.length,e.endsWith(c)?s=t:(t<g||g>e.length-c.length)&&(s=g),e=e.substring(0,s),n=i,void 0===(v=u?"-"+e:e)&&(v=""),m=new RegExp("(-)"),a=new RegExp("(-)(.)*(-)"),m=m.test(v),a=a.test(v),v=v.replace(/-/g,""),o=(e=((e=v=m&&!a&&n?"-"+v:v).match((t=!0,new RegExp("(^-)|[0-9]|"+h(r),t?"g":void 0)))||[]).join("")).indexOf(r),s=(g=S(e=e.replace(new RegExp(h(r),"g"),function(e,t){return t===o?".":""}),i)).beforeDecimal,u=g.afterDecimal,m=g.addNegation,d.end-d.start<f.end-f.start&&""===s&&p&&!parseFloat(u)&&(e=m?"-":"")),e}function A(e,t){var r=t.prefix,t=(void 0===r&&(r=""),t.suffix),n=(void 0===t&&(t=""),Array.from({length:e.length+1}).map(function(){return!0})),a="-"===e[0],r=(n.fill(!1,0,r.length+(a?1:0)),e.length);return n.fill(!1,r-t.length+1,r+1),n}function i(a){(a=function(e){var t=(r=F(e)).thousandSeparator,r=r.decimalSeparator,n=e.prefix,a=(void 0===n&&(n=""),e.allowNegative);if(void 0===a&&(a=!0),t===r)throw new Error("\n        Decimal separator can't be same as thousand separator.\n        thousandSeparator: "+t+' (thousandSeparator = {true} is same as thousandSeparator = ",")\n        decimalSeparator: '+r+" (default value for decimalSeparator is .)\n     ");return n.startsWith("-")&&a&&(console.error("\n      Prefix can't start with '-' when allowNegative is true.\n      prefix: "+n+"\n      allowNegative: "+a+"\n    "),a=!1),Object.assign(Object.assign({},e),{allowNegative:a})}(a)).decimalSeparator,a.allowedDecimalSeparators,a.thousandsGroupStyle;function e(e){return C(e,a)}function l(e,t){return j(e,t,a)}function t(e){return!D(e)&&("number"==typeof e&&(e=O(e)),V&&"number"==typeof f)?E(e,f,Boolean(d)):e}var r=a.suffix,u=a.allowNegative,o=a.allowLeadingZeros,i=a.onKeyDown,c=(void 0===i&&(i=G),a.onBlur),s=(void 0===c&&(c=G),a.thousandSeparator),f=a.decimalScale,d=a.fixedDecimalScale,m=a.prefix,n=(void 0===m&&(m=""),a.defaultValue),g=a.value,v=a.valueIsNumericString,p=a.onValueChange,h=$(a,["decimalSeparator","allowedDecimalSeparators","thousandsGroupStyle","suffix","allowNegative","allowLeadingZeros","onKeyDown","onBlur","thousandSeparator","decimalScale","fixedDecimalScale","prefix","defaultValue","value","valueIsNumericString","onValueChange"]),S=F(a),y=S.decimalSeparator,b=S.allowedDecimalSeparators,S=N(g)?n:g,V=null!=v?v:(v=m,r=r,""===(S=S)||!(null!=v&&v.match(/\d/)||null!=r&&r.match(/\d/)||"string"!=typeof S||isNaN(Number(S)))),v=(N(g)?N(n)||(V=V||"number"==typeof n):V=V||"number"==typeof g,H(t(g),t(n),Boolean(V),e,l,p)),r=v[0],w=r.numAsString,S=r.formattedValue,x=v[1];return Object.assign(Object.assign({},h),{value:S,valueIsNumericString:!1,isValidInputCharacter:function(e){return e===y||U(e)},isCharacterSame:function(e){function t(e){return l(e).indexOf(".")+m.length}var r=e.currentValue,n=e.lastValue,a=e.formattedValue,o=e.currentValueIndex,e=e.formattedValueIndex,u=r[o],i=a[e],n=Z(n,r).to;return!(0===g&&d&&f&&r[n.start]===y&&t(r)<o&&t(a)>e)&&(!!(o>=n.start&&o<n.end&&b&&b.includes(u)&&i===y)||u===i)},onValueChange:x,format:e,removeFormatting:l,getCaretBoundary:function(e){return A(e,a)},onKeyDown:function(e){var t=e.target,r=e.key,n=t.selectionStart,a=t.selectionEnd,o=t.value;void 0===o&&(o=""),("Backspace"===r||"Delete"===r)&&a<m.length?e.preventDefault():(n===a&&("Backspace"===r&&"-"===o[0]&&n===m.length+1&&u&&q(t,1),f&&d&&("Backspace"===r&&o[n-1]===y?(q(t,n-1),e.preventDefault()):"Delete"===r&&o[n]===y&&e.preventDefault()),null!=b&&b.includes(r)&&o[n]===y&&q(t,n+1),a=!0===s?",":s,"Backspace"===r&&o[n-1]===a&&q(t,n-1),"Delete"===r&&o[n]===a&&q(t,n+1)),i(e))},onBlur:function(e){var t,r,n=w;n.match(/\d/g)||(n=""),o||(n=(r=n)&&((t="-"===r[0])?"-":"")+((t=(r=t?r.substring(1,r.length):r).split("."))[0].replace(/^0+/,"")||"0")+((t=t[1]||"")?"."+t:"")),(n=d&&f?E(n,f,d):n)!==w&&(r=C(n,a),x({formattedValue:r,value:n,floatValue:parseFloat(n)},{event:e,source:W.event})),c(e)}})}function g(e,t){var r=t.format,n=t.allowEmptyFormatting,a=t.mask,o=t.patternChar;if(void 0===o&&(o="#"),""===e&&!n)return"";for(var u=0,i=r.split(""),l=0,c=r.length;l<c;l++)r[l]===o&&(i[l]=e[u]||m(a,u),u+=1);return i.join("")}function v(e,t,r){void 0===t&&(t=y(e));function a(e){return i[e]===l}function n(e,t){for(var r="",n=0;n<e.length;n++)a(t+n)&&U(e[n])&&(r+=e[n]);return r}function o(e){return e.replace(/[^0-9]/g,"")}var u,i=r.format,l=r.patternChar,r=(void 0===l&&(l="#"),t.from),c=t.to,t=t.lastValue;void 0===t&&(t="");if(!i.match(/\d/))return o(e);if(""!==t&&r.end-r.start!==t.length||e.length!==i.length)return u=t.substring(0,r.start),c=e.substring(c.start,c.end),t=t.substring(r.end),""+n(u,0)+o(c)+n(t,r.end);for(var s="",f=0;f<e.length;f++)if(a(f))U(e[f])&&(s+=e[f]);else if(e[f]!==i[f])return o(e);return s}function p(n,e){for(var t=e.format,a=e.mask,o=e.patternChar,r=(void 0===o&&(o="#"),Array.from({length:n.length+1}).map(function(){return!0})),u=0,i=-1,l={},c=(t.split("").forEach(function(e,t){var r=void 0;e===o&&(r=m(a,++u-1),-1===i&&n[t]===r&&(i=t)),l[t]=r}),function(e){return t[e]===o&&n[e]!==l[e]}),s=0,f=r.length;s<f;s++)r[s]=s===i||c(s)||c(s-1);return r[t.indexOf(o)]=!0,r}function l(t){t.mask,t.allowEmptyFormatting;var l=t.format,e=t.inputMode,c=(void 0===e&&(e="numeric"),t.onKeyDown),s=(void 0===c&&(c=G),t.patternChar),r=(void 0===s&&(s="#"),t.value),n=t.defaultValue,a=t.valueIsNumericString,o=$(t,["mask","allowEmptyFormatting","format","inputMode","onKeyDown","patternChar","value","defaultValue","valueIsNumericString"]),u=t;if((u=u.mask)&&("string"===u?u:u.toString()).match(/\d/g))throw new Error("Mask "+u+" should not contain numeric character;");function f(e){return p(e,t)}var u=N(r)?n:r,a=null!=a?a:(a=l,""===(u=u)||!(null!=a&&a.match(/\d/)||"string"!=typeof u||!u.match(/^\d+$/)&&""!==u)),i=Object.assign(Object.assign({},t),{valueIsNumericString:a});return Object.assign(Object.assign({},o),{value:r,defaultValue:n,valueIsNumericString:a,inputMode:e,format:function(e){return g(e,i)},removeFormatting:function(e,t){return v(e,t,i)},getCaretBoundary:f,onKeyDown:function(e){var t=e.key,r=e.target,n=r.selectionStart,a=r.selectionEnd,o=r.value;if(n===a){var u=n;if("Backspace"===t||"Delete"===t){a="right";if("Backspace"===t){for(;0<u&&l[u-1]!==s;)u--;a="left"}else{for(var i=l.length;u<i&&l[u]!==s;)u++;a="right"}u=z(o,u,f(o),a)}else l[u]!==s&&"ArrowLeft"!==t&&"ArrowRight"!==t&&(u=z(o,u+1,f(o),"right"));u!==n&&q(r,u)}c(e)}})}e.NumberFormatBase=u,e.NumericFormat=function(e){return e=i(e),L.createElement(u,Object.assign({},e))},e.PatternFormat=function(e){return e=l(e),L.createElement(u,Object.assign({},e))},e.getNumericCaretBoundary=A,e.getPatternCaretBoundary=p,e.numericFormatter=C,e.patternFormatter=g,e.removeNumericFormat=j,e.removePatternFormat=v,e.useNumericFormat=i,e.usePatternFormat=l,Object.defineProperty(e,"__esModule",{value:!0})});
